{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "xxx API Documentation", "title": "xxx API", "contact": {}, "version": "1.0"}, "host": "localhost:8000", "basePath": "/api/v1", "paths": {"/check-active/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Check Device for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "for Check Device", "parameters": [{"type": "string", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/check-device": {"post": {"security": [{"BearerAuth": []}], "description": "Check Active for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "for Check Active", "parameters": [{"type": "string", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/login-code": {"post": {"security": [{"BearerAuth": []}], "description": "Login for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "for Login", "parameters": [{"description": "Login With Code", "name": "payload", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.GetCodeReq"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/dtos.GetCodeResp"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/logout-device": {"post": {"security": [{"BearerAuth": []}], "description": "Logout Device for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "for Logout Device", "parameters": [{"type": "string", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/presences": {"post": {"security": [{"BearerAuth": []}], "description": "Get Presence By Phone", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "Get Presence By Phone", "parameters": [{"description": "Get Presence By Phone", "name": "payload", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.GetPresenceReq"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/profile-photo/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get Profile for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "summary for Get Profile", "parameters": [{"type": "string", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/subscribe-presence": {"post": {"security": [{"BearerAuth": []}], "description": "Subscribe Presence for wp", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["WP Endpoints"], "summary": "for Subscribe Presence", "parameters": [{"description": "Subscribe Presence", "name": "payload", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.SubscribePresenceReq"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"dtos.GetCodeReq": {"type": "object", "properties": {"phone": {"type": "string"}}}, "dtos.GetCodeResp": {"type": "object", "properties": {"code": {"type": "string"}, "reg_id": {"type": "string"}}}, "dtos.GetPresenceReq": {"type": "object", "properties": {"page": {"type": "integer"}, "perpage": {"type": "integer"}, "phone": {"type": "string"}, "reg_id": {"type": "string"}}}, "dtos.SubscribePresenceReq": {"type": "object", "properties": {"reg_id": {"type": "string"}, "subscribe_phone": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}