basePath: /api/v1
definitions:
  dtos.GetCodeReq:
    properties:
      phone:
        type: string
    type: object
  dtos.GetCodeResp:
    properties:
      code:
        type: string
      reg_id:
        type: string
    type: object
  dtos.GetPresenceReq:
    properties:
      page:
        type: integer
      perpage:
        type: integer
      phone:
        type: string
      reg_id:
        type: string
    type: object
  dtos.SubscribePresenceReq:
    properties:
      reg_id:
        type: string
      subscribe_phone:
        type: string
    type: object
host: localhost:8000
info:
  contact: {}
  description: xxx API Documentation
  title: xxx API
  version: "1.0"
paths:
  /check-active/{id}:
    get:
      consumes:
      - application/json
      description: Check Device for wp
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: for Check Device
      tags:
      - WP Endpoints
  /check-device:
    post:
      consumes:
      - application/json
      description: Check Active for wp
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: for Check Active
      tags:
      - WP Endpoints
  /login-code:
    post:
      consumes:
      - application/json
      description: Login for wp
      parameters:
      - description: Login With Code
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.GetCodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/dtos.GetCodeResp'
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: for Login
      tags:
      - WP Endpoints
  /logout-device:
    post:
      consumes:
      - application/json
      description: Logout Device for wp
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: for Logout Device
      tags:
      - WP Endpoints
  /presences:
    post:
      consumes:
      - application/json
      description: Get Presence By Phone
      parameters:
      - description: Get Presence By Phone
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.GetPresenceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get Presence By Phone
      tags:
      - WP Endpoints
  /profile-photo/{id}:
    get:
      consumes:
      - application/json
      description: Get Profile for wp
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: summary for Get Profile
      tags:
      - WP Endpoints
  /subscribe-presence:
    post:
      consumes:
      - application/json
      description: Subscribe Presence for wp
      parameters:
      - description: Subscribe Presence
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.SubscribePresenceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: for Subscribe Presence
      tags:
      - WP Endpoints
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
