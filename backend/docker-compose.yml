version: "3"
services:
  wp-core-db:
    image: "postgres:14.6"
    container_name: wp-core-db
    volumes:
      - wp_core_data:/var/lib/postgresql/data
    networks:
      - saye
    restart: always
    ports:
      - "127.0.0.1:5436:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"

  orion-wp-core:
    build:
      context: .
      dockerfile: Dockerfile
    image: orion-wp-core
    environment:
      - TZ="Europe/Istanbul"
    container_name: orion-wp-core
    restart: always
    networks:
      - saye
    volumes:
      - ./:/app
      - ./config.yaml:/app/config.yaml
    ports:
      - 8023:8023
    depends_on:
      - wp-core-db

volumes:
  wp_core_data:

networks:
  saye:
    name: saye
    driver: bridge