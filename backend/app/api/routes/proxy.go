package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/sayeworldevelopment/wp-core/pkg/domains/proxy"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/middleware"
)

func ProxyRoutes(r *gin.RouterGroup, s proxy.Service) {
	r.POST("/proxy", middleware.AdminKey(), ProxyAdd(s))
}

func ProxyAdd(s proxy.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RequestForManualProxyAdd
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "ProxyAdd",
				Message: "ProxyAdd bind json err:" + err.Error(),
				Entity:  "proxy",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		err := s.AddProxy(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "ProxyAdd",
				Message: "ProxyAdd err:" + err.Error(),
				Entity:  "proxy",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "ProxyAdd",
			Message: "Proxy added Success",
			Entity:  "proxy",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{
			"data":   "sucessfully added",
			"status": 201,
		})

	}
}
