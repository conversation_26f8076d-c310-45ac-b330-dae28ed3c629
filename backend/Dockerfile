FROM golang:1.24-alpine AS builder

RUN apk add --no-cache upx
RUN apk --no-cache add tzdata

WORKDIR /src

COPY . .

RUN go mod download

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o orion-wp-core main.go
RUN upx orion-wp-core


FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION
ENV DEV_MODE=true

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/orion-wp-core

COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /src/orion-wp-core .

COPY config.yaml /bin/orion-wp-core/


CMD ["./orion-wp-core"]
