package utils

import (
	"strings"
	"time"
)

func GetNow() string {
	return time.Now().Format("2006-01-02 15:04:05 -0700 -07")
}

func StringToTime(rawDate string) time.Time {
	rawDate = strings.ReplaceAll(rawDate, "T", " ")
	rawDate = strings.ReplaceAll(rawDate, "Z", " ")
	rawDate = rawDate + " +0300 +03"
	rawDateTime, _ := time.Parse("2006-01-02 15:04:05 -0700 -07", rawDate)

	return rawDateTime
}
