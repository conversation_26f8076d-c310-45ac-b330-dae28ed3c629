package utils

import (
	"context"

	"github.com/cloudinary/cloudinary-go"
	"github.com/cloudinary/cloudinary-go/api/uploader"
	"github.com/go-playground/validator/v10"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
)

func CloudinaryImageUpload(ctx context.Context, file interface{}) (*uploader.UploadResult, error) {
	var validate = validator.New()
	if err := validate.Struct(file); err != nil {
		return nil, err
	}
	cld, err := cloudinary.NewFromParams(
		config.InitConfig().Cloudinary.Name,
		config.InitConfig().Cloudinary.APIKey,
		config.InitConfig().Cloudinary.APISecret,
	)
	if err != nil {
		return nil, err
	}
	uploadParam, err := cld.Upload.Upload(ctx, file, uploader.UploadParams{Folder: config.InitConfig().Cloudinary.APIFolder})
	if err != nil {
		return nil, err
	}
	return uploadParam, nil
}

func CloudinaryRemoveImage(ctx context.Context, publicID string) error {
	cld, err := cloudinary.NewFromParams(
		config.InitConfig().Cloudinary.Name,
		config.InitConfig().Cloudinary.APIKey,
		config.InitConfig().Cloudinary.APISecret,
	)
	if err != nil {
		return err
	}
	_, err = cld.Upload.Destroy(ctx, uploader.DestroyParams{
		PublicID: publicID,
	})
	if err != nil {
		return err
	}
	return nil
}
