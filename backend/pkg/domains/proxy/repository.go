package proxy

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	AddProxy(ctx context.Context, req dtos.RequestForManualProxyAdd) error
	DeleteProxy(ctx context.Context, id string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) AddProxy(ctx context.Context, req dtos.RequestForManualProxyAdd) error {
	var (
		proxy_control, new_proxy entities.Proxy
	)

	r.db.WithContext(ctx).
		Model(&entities.Proxy{}).
		Where("name = ? AND username = ?", req.Name, req.Username).
		First(&proxy_control)

	if proxy_control.ID != uuid.Nil {
		return errors.New("proxy already exists")
	}

	new_proxy.Name = req.Name
	new_proxy.Country = req.Country
	new_proxy.Scheme = req.Scheme
	new_proxy.Host = req.Host
	new_proxy.Port = req.Port
	new_proxy.Username = req.Username
	new_proxy.Password = req.Password
	new_proxy.Default = req.Default
	new_proxy.Active = false // default

	// TODO: maybe we can add healthy check here

	if err := r.db.WithContext(ctx).
		Create(&new_proxy).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) DeleteProxy(ctx context.Context, id string) error {
	var (
		proxy_control entities.Proxy
	)

	r.db.WithContext(ctx).
		Model(&entities.Proxy{}).
		Where("id = ?", id).
		First(&proxy_control)

	if proxy_control.ID == uuid.Nil {
		return errors.New("proxy not found")
	}

	if err := r.db.WithContext(ctx).
		Delete(&proxy_control).Error; err != nil {
		return err
	}

	// TODO: bu proxye bağlı olan tüm sessionları güncelle

	return nil
}
