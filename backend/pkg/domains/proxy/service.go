package proxy

import (
	"context"

	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
)

type Service interface {
	AddProxy(ctx context.Context, req dtos.RequestForManualProxyAdd) error
	DeleteProxy(ctx context.Context, id string) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) AddProxy(ctx context.Context, req dtos.RequestForManualProxyAdd) error {
	return s.repository.AddProxy(ctx, req)
}

func (s *service) DeleteProxy(ctx context.Context, id string) error {
	return s.repository.DeleteProxy(ctx, id)
}
