package wp

import (
	"context"
	"errors"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
)

type Service interface {
	GetProfile(ctx context.Context, id string) (string, error)
	GetProfileByPhone(ctx context.Context, regId string, phoneNumber string) (string, error)
	GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error)
	CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) (dtos.CheckDeviceResp, bool)
	IsActive(ctx context.Context, regId string) (dtos.DeviceResponse, bool)
	LogoutDevice(ctx context.Context, regId string) (bool, error)
	GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error)
	SubscribePresence(ctx context.Context, req dtos.SubscribePresenceReq) error
	RemovePresenceSubscription(ctx context.Context, req dtos.RemovePresenceSubscriptionReq) error
	GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error)

	// Session management

}

type service struct {
	repo Repository
	wp   *wrapper.Client
	//sessionSvc Service
}

func NewService(r Repository, wp *wrapper.Client) Service {
	// Create the session service
	//sessionSvc := NewService(r, wp)

	return &service{
		repo: r,
		wp:   wp,
		//sessionSvc: sessionSvc,
	}
}

func (s *service) GetProfile(ctx context.Context, id string) (string, error) {

	device, err := s.repo.FindActiveDeviceByRegID(ctx, id)
	if err != nil || device.RegistrationID == "" {
		return "", err
	}

	photo, err := s.wp.GetProfilePhoto(ctx, device.JID)
	if err != nil {
		return photo, err
	}
	return photo, nil
}

func (s *service) GetProfileByPhone(ctx context.Context, regId string, phoneNumber string) (string, error) {
	device, err := s.repo.FindActiveDeviceByRegID(ctx, regId)
	if err != nil || device.RegistrationID == "" {
		return "", err
	}

	photo, err := s.wp.GetProfilePhotoByPhone(ctx, device.JID, phoneNumber)
	if err != nil {
		return photo, err
	}
	return photo, nil
}

func (s *service) GetCode(ctx context.Context, req dtos.GetCodeReq) (dtos.GetCodeResp, error) {
	resp, err := s.wp.GetCode(ctx, req)
	if err != nil {
		return resp, err
	}
	return resp, nil
}

func (s *service) CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) (dtos.CheckDeviceResp, bool) {
	var (
		device dtos.DeviceIsActive
		err    error
		resp   dtos.CheckDeviceResp
	)

	// Create a timeout context
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	for {
		select {
		case <-ctx.Done():
			if ctx.Err() == context.DeadlineExceeded {
				log.Println("Timeout reached (30s). Exiting CheckDevice.")
			} else {
				log.Println("Context is canceled. Exiting CheckDevice.")
			}
			return resp, false
		default:
		}

		time.Sleep(200 * time.Millisecond)
		device, err = s.repo.FindDeviceByRegID(ctx, req.RegistrationID)
		if err != nil || device.RegistrationID == "" {
			continue
		}
		break
	}

	// Get the active device with more details
	activeDevice, err := s.repo.FindDeviceByRegID(ctx, req.RegistrationID)
	if err != nil || activeDevice.RegistrationID == "" {
		return resp, false
	}

	_, isLoggedIn := s.wp.CheckDevice(ctx, device.JID, req.RegistrationID)

	entDevice, err := s.repo.GetEntDevice(ctx, activeDevice)
	if err != nil {
		return resp, false
	}
	resp = entDevice.ToCheckDeviceDto()
	resp.IsLoggedIn = isLoggedIn

	return resp, isLoggedIn
}

func (s *service) IsActive(ctx context.Context, regId string) (dtos.DeviceResponse, bool) {
	var res dtos.DeviceResponse
	device, err := s.repo.FindDeviceByRegID(ctx, regId)
	if err != nil || device.RegistrationID == "" {

		return res, false
	}
	_, isLoggedIn := s.wp.CheckDevice(ctx, device.JID, regId)
	res.Mapper(device, isLoggedIn)
	return res, isLoggedIn
}

func (s *service) LogoutDevice(ctx context.Context, regId string) (bool, error) {
	device, err := s.repo.FindDeviceByRegID(ctx, regId)
	if err != nil {
		return false, err
	}
	client, isConnected := s.wp.CheckDevice(ctx, device.JID, regId)
	if !isConnected {
		return false, errors.New("device not connected")
	}
	err = client.Logout(ctx)
	if err != nil {
		return false, err
	}
	// Clean up from wrapper's active clients
	s.wp.CleanupClientForJID(device.JID)

	if err := s.repo.UpdateLoginCode(ctx, regId); err != nil {
		return false, err
	}

	return true, nil
}

func (s *service) GetDevices(ctx context.Context, regIds []string, page int) ([]entities.Device, error) {
	var devicesRes []entities.Device
	devices, err := s.repo.GetDevices(ctx, regIds, page)
	if err != nil {
		return devices, err
	}

	for _, device := range devices {
		_, isActive := s.wp.CheckDevice(ctx, device.JID, device.RegistrationID)
		if isActive {
			device.State = consts.DeviceOnline
			devicesRes = append(devicesRes, device)
		} else {
			// Clean up from wrapper's active clients if not active
			s.wp.CleanupClientForJID(device.JID)
		}

	}
	return devicesRes, nil
}

func (s *service) SubscribePresence(ctx context.Context, req dtos.SubscribePresenceReq) error {
	device, err := s.repo.FindDeviceByRegID(ctx, req.RegistrationID)
	if err != nil || device.RegistrationID == "" {
		return err
	}

	_, err = s.wp.SubscribePresence(ctx, device.JID, req.SubscribePhone)

	return err
}

func (s *service) RemovePresenceSubscription(ctx context.Context, req dtos.RemovePresenceSubscriptionReq) error {
	device, err := s.repo.FindDeviceByRegID(ctx, req.RegistrationID)
	if err != nil || device.RegistrationID == "" {
		return err
	}

	err = s.wp.RemovePresenceSubscription(ctx, device.JID, req.SubscribePhone, 0)

	return err
}

func (s *service) GetPresences(ctx context.Context, req dtos.GetPresenceReq) (dtos.PaginatedData, error) {
	return s.repo.GetPresences(ctx, req)
}

//get session with registration id
