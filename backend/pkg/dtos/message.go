package dtos

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type SendSingleMessageReq struct {
	RegID     string `json:"reg_id" form:"reg_id" validate:"required"`
	To        string `json:"to" form:"to" validate:"required"`
	Message   string `json:"message" form:"message" validate:"required"`
	File      string `json:"file"`
	SendSpeed string `json:"send_speed" form:"send_speed" validate:"required"`
	ReportId  string `json:"report_id" form:"report_id" validate:"required"`
	AppID     string `json:"app_id"`
	SendTime  string `json:"send_time" form:"send_time"`
}

type OneToNMessageReq struct {
	RegID       string   `json:"reg_id" form:"reg_id" validate:"required"`
	To          []string `json:"to"`
	ToStr       string   `form:"to" validate:"required"`
	Message     string   `json:"message" form:"message" validate:"required"`
	File        string   `json:"file"`
	SendSpeed   string   `json:"send_speed" form:"send_speed" validate:"required"`
	ReportId    string   `json:"report_id" form:"report_id" validate:"required"`
	AppID       string   `json:"app_id" form:"app_id"`
	CallbackUrl string   `json:"callback" form:"callback"`
	SendTime    string   `json:"send_time" form:"send_time"`
}

func (m *OneToNMessageReq) ConvertToSingle() []SendSingleMessageReq {
	var sendSingleMessageReq []SendSingleMessageReq
	for _, to := range m.To {
		var ssmq SendSingleMessageReq
		ssmq.AppID = m.AppID
		ssmq.RegID = m.RegID
		ssmq.Message = m.Message
		ssmq.File = m.File
		ssmq.To = to
		ssmq.SendSpeed = m.SendSpeed
		ssmq.ReportId = m.ReportId
		ssmq.SendTime = m.SendTime
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}

type AddNatsMessage struct {
	JID         string    `json:"jid"`
	To          string    `json:"to"`
	Message     string    `json:"message"`
	File        string    `json:"file"`
	ReportId    string    `json:"report_id"`
	AppID       string    `json:"app_id"`
	CallbackUrl string    `json:"callback"`
	Status      string    `json:"status"`
	SendTime    string    `json:"send_time"`
	MsgId       uuid.UUID `json:"_"`
	RegId       string    `json:"reg_id"`
}

type MessageApp struct {
	Status string `json:"status"`
	AppId  string `json:"app_id"`
}

func (a *AddNatsMessage) Mapper(c *gin.Context, message SendSingleMessageReq, device Device) {
	a.JID = device.JID
	a.AppID = message.AppID
	a.To = message.To
	a.Message = message.Message
	a.File = message.File
	a.ReportId = message.ReportId
	a.RegId = message.RegID
}

func (a *SendSingleMessageReq) FormMapper(regId, to, message, reportId, sendSpeed, file string, sendTime string) {
	a.RegID = regId
	a.To = to
	a.Message = message
	a.File = file
	a.SendSpeed = sendSpeed
	a.ReportId = reportId
	a.SendTime = sendTime
}
func (a *OneToNMessageReq) FormMapper(regId, message, sendSpeed, reportId string, to []string, file string, sendTime string) {
	a.RegID = regId
	a.To = to
	a.Message = message
	a.File = file
	a.SendSpeed = sendSpeed
	a.ReportId = reportId
	a.SendTime = sendTime
}

type NToNMessageReq struct {
	RegID       string           `json:"reg_id" form:"reg_id" validate:"required"`
	NtoNStr     string           `json:"-" form:"n_to_n" validate:"required"`
	NtoN        []NtoNMessageDto `json:"n_to_n" form:"-"`
	File        string           `json:"file" form:"file"`
	SendSpeed   string           `json:"send_speed" form:"send_speed" validate:"required"` ////1-slow 2- medium 3- fast
	ReportId    string           `json:"report_id" form:"report_id" validate:"required"`
	AppID       string           `json:"app_id"`
	CallbackUrl string           `json:"callback"`
	SendTime    string           `json:"send_time" form:"send_time"`
}

type NtoNMessageDto struct {
	To      string `json:"to"`
	Message string `json:"message"`
}

func (m *NToNMessageReq) ConvertToSingle() []SendSingleMessageReq {
	var sendSingleMessageReq []SendSingleMessageReq
	for _, to := range m.NtoN {

		toSplited := strings.ReplaceAll(to.To, "+", "")

		var ssmq SendSingleMessageReq
		ssmq.AppID = m.AppID
		ssmq.RegID = m.RegID
		ssmq.Message = to.Message
		ssmq.File = m.File
		ssmq.To = toSplited
		ssmq.SendSpeed = m.SendSpeed
		ssmq.ReportId = m.ReportId
		ssmq.SendTime = m.SendTime
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}

type FutureMessageDelete struct {
	ID    uint   `json:"id" query:"id" validate:"required"`
	AppID string `json:"app_id" form:"_"`
}

type PauseMessage struct {
	ID        uint   `json:"id" query:"id" validate:"required"`
	IsPause   bool   `json:"is_pause" validate:"required"` // true: paused, false: continue
	SendSpeed uint   `json:"send_speed" validate:"required"`
	AppID     string `json:"app_id" form:"_"`
}

type DeleteMessage struct {
	ID    uint   `json:"id" query:"id" validate:"required"`
	AppID string `json:"app_id" form:"_"`
}

type FutureMessageUpdate struct {
	ID        uint   `json:"id"  form:"id" validate:"required"`
	Name      string `json:"name" form:"name" validate:"required"`
	Numbers   string `json:"numbers" form:"numbers" validate:"required"`
	Message   string `json:"message" form:"message" validate:"required"`
	TimePost  string `json:"time_post" form:"time_post" validate:"required"`
	Type      uint   `json:"type" form:"type" validate:"required"`
	SendSpeed uint   `json:"send_speed" form:"send_speed" validate:"omitempty"` //1-hızlı 2- orta 3- yavaş
	RegId     string `json:"reg_id" form:"reg_id"`
	AppID     string `json:"app_id" form:"_"`
	File      string `json:"file" form:"_"`
}

func (fmu *FutureMessageUpdate) ConvertToSingle() []SendSingleMessageReq {
	var sendSingleMessageReq []SendSingleMessageReq
	numbers := strings.Split(fmu.Numbers, ",")
	for _, to := range numbers {
		var ssmq SendSingleMessageReq
		ssmq.AppID = fmu.AppID
		ssmq.RegID = fmu.RegId
		ssmq.Message = fmu.Message
		ssmq.File = fmu.File
		ssmq.To = to
		ssmq.SendSpeed = "1"
		ssmq.ReportId = strconv.FormatUint(uint64(fmu.ID), 10)
		ssmq.SendTime = fmu.TimePost
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}
