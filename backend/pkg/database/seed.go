package database

import (
	"log"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
)

func InitSeed() {

	// should be fetched from json file
	proxies := []entities.Proxy{
		{
			Name:             "isp_proxy_turkey",
			Country:          "TR",
			Scheme:           "http",
			Host:             "brd.superproxy.io",
			Port:             "33335",
			Username:         "brd-customer-hl_e485892a-zone-isp_proxy_turkey",
			Password:         "u9m6d531s8u5",
			IsHealthy:        false,
			LastHealthyCheck: time.Now(),
			UserCount:        0,
			Default:          true,
			Active:           true,
		},
		{
			Name:             "isp_proxy_poland",
			Country:          "PL",
			Scheme:           "http",
			Host:             "brd.superproxy.io",
			Port:             "33335",
			Username:         "brd-customer-hl_e485892a-zone-isp_proxy_poland",
			Password:         "p9i36yub1ebm",
			IsHealthy:        false,
			LastHealthyCheck: time.Now(),
			UserCount:        0,
			Default:          false,
			Active:           false,
		},
		{
			Name:             "isp_proxy_def_europe_germany",
			Country:          "DE",
			Scheme:           "http",
			Host:             "brd.superproxy.io",
			Port:             "33335",
			Username:         "brd-customer-hl_e485892a-zone-isp_proxy_def_europe_germany",
			Password:         "gg2exr0ta0ox",
			IsHealthy:        false,
			LastHealthyCheck: time.Now(),
			UserCount:        0,
			Default:          true,
			Active:           false,
		},
	}

	db := DBClient()

	for _, proxy := range proxies {

		var control_proxy entities.Proxy
		db.Model(&entities.Proxy{}).Debug().
			Where("name = ?", proxy.Name).
			First(&control_proxy)

		if control_proxy.ID != uuid.Nil {
			continue
		}

		if err := db.Model(&entities.Proxy{}).
			Create(&proxy).Error; err != nil {
			log.Printf("Failed to seed proxy %s: %v", proxy.Name, err)
		} else {
			log.Printf("Seeded proxy %s", proxy.Name)

		}
	}

}
