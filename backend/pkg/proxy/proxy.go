package proxy

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"gorm.io/gorm"
)

func SetUrlForSession(ctx context.Context, session_id uuid.UUID) (func(*http.Request) (*url.URL, error), error) {
	var (
		proxy           entities.Proxy
		current_session entities.Session
	)

	log.Println("session_id:", session_id)

	db := database.DBClient()
	if err := db.WithContext(ctx).
		Model(&entities.Session{}).
		Where("id = ?", session_id).
		First(&current_session).Error; err != nil {
		return nil, err
	}

	if err := db.WithContext(ctx).
		Model(&entities.Proxy{}).
		Where("id = ?", current_session.ProxyID).
		First(&proxy).Error; err != nil {
		return nil, err
	}

	proxy_url := http.ProxyURL(&url.URL{
		Scheme: "http",
		Host:   fmt.Sprintf("%s:%s", proxy.Host, proxy.Port),
		User:   url.UserPassword(proxy.Username, proxy.Password),
	})

	return proxy_url, nil
}

func SetUrlWithoutSession(ctx context.Context, ip, phone_number string) (func(*http.Request) (*url.URL, error), error) {
	/*
		1. check ip address,
		2. if we get error, check phone number,
	*/
	var (
		proxy        entities.Proxy
		country_code string
	)

	country_code, err := DetectCountryByIP(ip)
	if err != nil {
		// If we cannot detect country by IP, try to detect it by phone number
		country_code, err = DetectCountryByPhone(phone_number)
		if err != nil {
			return nil, err
		}
	}

	log.Println("Detected country code:", country_code)

	db := database.DBClient()
	if err := db.WithContext(ctx).
		Model(&entities.Proxy{}).
		Where("country = ?", country_code).
		Where("active = ?", true).
		Order("user_count ASC").
		First(&proxy).Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			// If no proxy found for the country code, try to find a default proxy from europe
			if err := db.WithContext(ctx).
				Model(&entities.Proxy{}).
				Where("default = ? AND active = ?", true, true).
				Order("user_count ASC").
				First(&proxy).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	proxy_url := http.ProxyURL(&url.URL{
		Scheme: "http",
		Host:   fmt.Sprintf("%s:%s", proxy.Host, proxy.Port),
		User:   url.UserPassword(proxy.Username, proxy.Password),
	})

	return proxy_url, nil
}

func SetProxyInfoForSession(ctx context.Context, ip, phone string) (uuid.UUID, string, error) {
	/*
		1. check ip address,
		2. if we get error, check phone number,
	*/
	var (
		proxy        entities.Proxy
		country_code string
	)

	country_code, err := DetectCountryByIP(ip)
	if err != nil {
		// If we cannot detect country by IP, try to detect it by phone number
		country_code, err = DetectCountryByPhone(phone)
		if err != nil {
			return uuid.Nil, "", err
		}
	}

	db := database.DBClient()
	if err := db.WithContext(ctx).
		Model(&entities.Proxy{}).
		Where("country = ?", country_code).
		Where("active = ?", true).
		Order("user_count ASC").
		First(&proxy).Error; err != nil {

		if err == gorm.ErrRecordNotFound {
			// If no proxy found for the country code, try to find a default proxy from europe
			if err := db.WithContext(ctx).
				Model(&entities.Proxy{}).
				Where("default = ? AND active = ?", true, true).
				Order("user_count ASC").
				First(&proxy).Error; err != nil {
				return uuid.Nil, "", err
			}
		} else {
			return uuid.Nil, "", err
		}
	}

	return proxy.ID, fmt.Sprintf("%s:%s", proxy.Host, proxy.Port), nil
}
