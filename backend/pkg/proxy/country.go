package proxy

const (
	COUNTRY_TR      = "TR"
	COUNTRY_US      = "US"
	COUNTRY_GB      = "GB"
	COUNTRY_DE      = "DE"
	COUNTRY_FR      = "FR"
	COUNTRY_IT      = "IT"
	COUNTRY_ES      = "ES"
	COUNTRY_RU      = "RU"
	COUNTRY_CN      = "CN"
	COUNTRY_JP      = "JP"
	COUNTRY_KR      = "KR"
	COUNTRY_IN      = "IN"
	COUNTRY_BR      = "BR"
	COUNTRY_CA      = "CA"
	COUNTRY_AU      = "AU"
	COUNTRY_NL      = "NL"
	COUNTRY_BE      = "BE"
	COUNTRY_CH      = "CH"
	COUNTRY_AT      = "AT"
	COUNTRY_SE      = "SE"
	COUNTRY_NO      = "NO"
	COUNTRY_DK      = "DK"
	COUNTRY_FI      = "FI"
	COUNTRY_PL      = "PL"
	COUNTRY_CZ      = "CZ"
	COUNTRY_HU      = "HU"
	COUNTRY_GR      = "GR"
	COUNTRY_PT      = "PT"
	COUNTRY_IE      = "IE"
	COUNTRY_LU      = "LU"
	COUNTRY_UNKNOWN = "UNKNOWN"
)

// Country codes in E.164 format (without the '+' sign)
var CountryCodeMap = map[string]string{
	// Turkey
	"90": COUNTRY_TR,

	// North America
	"1": COUNTRY_US, // -> can be special case

	// Europe
	"44":  COUNTRY_GB, // United Kingdom
	"49":  COUNTRY_DE, // Germany
	"33":  COUNTRY_FR, // France
	"39":  COUNTRY_IT, // Italy
	"34":  COUNTRY_ES, // Spain
	"31":  COUNTRY_NL, // Netherlands
	"32":  COUNTRY_BE, // Belgium
	"41":  COUNTRY_CH, // Switzerland
	"43":  COUNTRY_AT, // Austria
	"46":  COUNTRY_SE, // Sweden
	"47":  COUNTRY_NO, // Norway
	"45":  COUNTRY_DK, // Denmark
	"358": COUNTRY_FI, // Finland
	"48":  COUNTRY_PL, // Poland
	"420": COUNTRY_CZ, // Czech Republic
	"36":  COUNTRY_HU, // Hungary
	"30":  COUNTRY_GR, // Greece
	"351": COUNTRY_PT, // Portugal
	"353": COUNTRY_IE, // Ireland
	"352": COUNTRY_LU, // Luxembourg

	// Asia
	"7":  COUNTRY_RU, // Russia (and some other former USSR countries) -> can be special case
	"86": COUNTRY_CN, // China
	"81": COUNTRY_JP, // Japan
	"82": COUNTRY_KR, // South Korea
	"91": COUNTRY_IN, // India

	// Others
	"55": COUNTRY_BR, // Brazil
	"61": COUNTRY_AU, // Australia
}
