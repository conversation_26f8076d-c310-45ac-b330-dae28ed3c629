package proxy

import (
	"encoding/json"
	"errors"
	"net/http"
	"strings"
)

func DetectCountryByIP(ip string) (string, error) {
	var country_code string
	if ip == "" {
		return country_code, errors.New("country not found for the given IP address")
	}

	country_code, err := getCountryByIP(ip)
	if err != nil {
		return country_code, err
	}

	// can return empty string and error nil
	if country_code == "" {
		return country_code, errors.New("country not found for the given IP address")
	}

	return country_code, nil
}

func DetectCountryByPhone(phone_number string) (string, error) {
	if IsValidE164(phone_number) {
		country := GetCountryOnly(phone_number)
		if country != COUNTRY_UNKNOWN {
			return country, nil
		} else {
			return "", errors.New("country not found for the given phone number")
		}
	}
	return "", errors.New("invalid phone number format")
}

func getCountryByIP(ip string) (string, error) {
	var info IPInfo
	resp, err := http.Get("https://ipinfo.io/" + ip + "/json")
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if err := json.NewDecoder(resp.Body).
		Decode(&info); err != nil {
		return "", err
	}

	return strings.ToUpper(info.Country), nil
}

func getCountryFromE164(phone_number string) E164PhoneNumber {
	result := E164PhoneNumber{
		Country: COUNTRY_UNKNOWN,
		Number:  phone_number,
		IsValid: false,
	}

	if phone_number == "" {
		return result
	}

	cleanNumber := strings.TrimPrefix(phone_number, "+")

	if !isNumeric(cleanNumber) {
		return result
	}

	if len(cleanNumber) < 7 {
		return result
	}

	countryCode, country := findCountryCode(cleanNumber)
	if country != COUNTRY_UNKNOWN {
		result.CountryPhoneCode = countryCode
		result.Country = country
		result.IsValid = true
	}

	return result
}

func GetCountryPhoneCodeOnly(phone_number string) string {
	result := getCountryFromE164(phone_number)
	return result.CountryPhoneCode
}

func GetCountryOnly(phone_number string) string {
	result := getCountryFromE164(phone_number)
	return result.Country
}

func IsValidE164(phone_number string) bool {
	result := getCountryFromE164(phone_number)
	return result.IsValid
}

func isNumeric(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return true
}

func findCountryCode(number string) (string, string) {
	for length := 4; length >= 1; length-- {
		if len(number) >= length {
			code := number[:length]
			if country, exists := CountryCodeMap[code]; exists {
				return code, country
			}
		}
	}

	return "", COUNTRY_UNKNOWN
}
