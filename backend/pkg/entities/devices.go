package entities

import (
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
)

type Device struct {
	Base
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	Platform       string `json:"platform"`
	PushName       string `json:"push_name"`
	BusinessName   string `json:"business_name"`
	Number         string `json:"device_number"`
	State          int    `json:"state"` // 1- online 2- çıkış
}

func (d *Device) FillWMDevice(device dtos.DeviceIsActive) {
	d.JID = device.JID
	d.RegistrationID = device.RegistrationID
	d.Platform = device.Platform
	d.PushName = device.PushName
	d.State = consts.DeviceOnline
}

func (d *Device) ToCheckDeviceDto() dtos.CheckDeviceResp {
	return dtos.CheckDeviceResp{
		RegistrationID: d.RegistrationID,
		CreatedAt:      d.<PERSON>t,
		Platform:       d.Platform,
		PushName:       d.PushName,
	}
}
