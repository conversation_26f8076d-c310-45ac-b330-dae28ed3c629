package entities

import (
	"strings"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
)

type Message struct {
	Base
	Content      string    `json:"content"`
	Phone        string    `json:"phone"`
	To           string    `json:"to"`
	AppID        string    `json:"app_id"`
	Status       string    `json:"status"`      // 1- success, 2 - fail
	IsCallback   int       `json:"is_callback"` //1- sended 2- not send
	CallbackData string    `json:"callback_data"`
	SendTime     string    `json:"send_time"`
	DeviceId     uuid.UUID `json:"device_id"`
	IsSended     bool      `json:"is_sended"`
	JID          string    `json:"jid"`
	RegId        string    `json:"reg_id"`
	File         string    `json:"file"`
	IsPause      bool      `json:"is_pause"` //0:paused, 1:continue
	TryCount     int       `json:"try_count"`
	Log          string    `json:"log"`
}

type Image struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name"`
	Data []byte `json:"data"`
}

func (m *Message) Mapper(req dtos.AddNatsMessage) {
	m.AppID = req.AppID
	m.Status = ""
	phone := strings.Split(req.JID, ":")
	m.Phone = phone[0]
	m.To = req.To
	m.Content = req.Message
	m.IsCallback = 2
	m.CallbackData = req.ReportId
	m.SendTime = req.SendTime
	m.RegId = req.RegId
}

func (a *Message) MapperFromMessage(message Message, device dtos.Device) dtos.AddNatsMessage {
	var addNats dtos.AddNatsMessage
	addNats.JID = device.JID
	addNats.AppID = message.AppID
	addNats.To = message.To
	addNats.Message = message.Content
	addNats.File = ""
	addNats.ReportId = message.CallbackData
	addNats.RegId = message.RegId
	return addNats
}

type FutureMessage struct {
	Base
	RegID     string `json:"reg_id"`
	To        string `json:"to"`
	Message   string `json:"message"`
	File      string `json:"file"`
	SendSpeed string `json:"send_speed"`
	ReportId  string `json:"report_id"`
	AppID     string `json:"app_id"`
	SendTime  string `json:"send_time"`
	Status    string `json:"status"` // 1:waiting, 2:processing, 3:complete
}

func (m *FutureMessage) Mapper(req dtos.SendSingleMessageReq) {
	m.RegID = req.RegID
	m.To = strings.TrimLeft(req.To, "+")
	m.Message = req.Message
	m.File = req.File
	m.SendSpeed = req.SendSpeed
	m.ReportId = req.ReportId
	m.AppID = req.AppID
	m.SendTime = req.SendTime
	m.Status = "1"
}

func (m *FutureMessage) ReverseMapToSingle() dtos.SendSingleMessageReq {
	var req dtos.SendSingleMessageReq
	req.AppID = m.AppID
	req.To = m.To
	req.Message = m.Message
	req.File = m.File
	req.SendSpeed = m.SendSpeed
	req.SendTime = m.SendTime
	req.RegID = m.RegID
	req.ReportId = m.ReportId
	return req
}

func ConvertFromSingleArrayToMessage(ssmr []dtos.SendSingleMessageReq, phone string) []Message {
	var messages []Message

	for _, v := range ssmr {
		var message Message
		message.AppID = v.AppID
		message.Phone = phone
		message.To = v.To
		message.Content = v.Message
		message.CallbackData = v.ReportId
		message.SendTime = v.SendTime
		message.Status = ""
		message.IsCallback = 2
		message.RegId = v.RegID
		message.File = v.File
		messages = append(messages, message)
	}
	return messages
}
