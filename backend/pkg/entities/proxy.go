package entities

import "time"

type Proxy struct {
	Base

	Name             string    `json:"name"`
	Country          string    `json:"country"`
	Scheme           string    `json:"scheme"`
	Host             string    `json:"host"`
	Port             string    `json:"port"`
	Username         string    `json:"username"`
	Password         string    `json:"password"`
	IsHealthy        bool      `json:"is_healthy"`
	LastHealthyCheck time.Time `json:"last_healthy_check"`
	UserCount        int       `json:"user_count"`
	Default          bool      `json:"default"`
	Active           bool      `json:"active"`
}
