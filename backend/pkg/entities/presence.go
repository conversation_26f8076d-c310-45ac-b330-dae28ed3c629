package entities

import (
	"time"

	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
)

type Presence struct {
	Base
	SessionId      string    `json:"session_id"`
	Status         string    `json:"status"`
	LastSeen       time.Time `json:"last_seen"`
	SubscribePhone string    `json:"subscribe_phone"`
}

func (p *Presence) ConvertDto() dtos.PresenceHistoryItem {
	var item dtos.PresenceHistoryItem
	item.Phone = p.SubscribePhone
	item.Status = p.Status
	item.Timestamp = p.CreatedAt
	if !p.LastSeen.IsZero() {
		item.LastSeen = &p.LastSeen
	}
	return item
}
