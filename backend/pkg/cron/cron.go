package cron

import (
	"fmt"
	"log"

	"github.com/go-co-op/gocron/v2"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

func MyCron() {
	s, _ := gocron.NewScheduler()
	j1, _ := s.<PERSON>ob(
		gocron.CronJob(
			`0 0 23 * * *`,
			false,
		),
		gocron.NewTask(
			zoneControl,
		),
	)
	log.Println(j1.ID())
	s.Start()
	log.Println("MyCron started...")
}

const (
	brightdata_url = "https://api.brightdata.com/%s"
)

func zoneControl() {
	log.Println("ZoneControl started...")

	var (
		resp_for_status dtos.ResponseForStatus
	)

	headers := map[string]string{
		"Authorization": config.InitConfig().BrightData.ApiKey,
	}

	if err := utils.Get(nil, fmt.Sprintf(brightdata_url, "status"), resp_for_status, headers); err != nil {
		return
	}

	log.Println("BrightData status:", resp_for_status.Status)
}
