name: Deployments
on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag to deploy'
        required: true

jobs:
  build:
    name: Deploy to Server
    runs-on: ubuntu-latest
    steps:
      - name: Deploy via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script_stop: true
          script: |
            cd project/wp-core-go
             if grep -q "^IMAGE_TAG=" .env; then
              sed -i 's/^IMAGE_TAG=.*/IMAGE_TAG=${{ inputs.tag }}/' .env
            else
              echo "IMAGE_TAG=${{ inputs.tag }}" >> .env
            fi


            docker stop orion-wp-core
            docker rm orion-wp-core
            docker compose --env-file .env up -d --build --no-deps orion-wp-core
